const LiveMap = () => {
  // Mock drone positions
  const dronePositions = [
    { id: 1, x: 15, y: 20 },
    { id: 2, x: 25, y: 35 },
    { id: 3, x: 45, y: 25 },
    { id: 4, x: 65, y: 40 },
    { id: 5, x: 75, y: 15 },
    { id: 6, x: 35, y: 60 },
    { id: 7, x: 55, y: 70 },
    { id: 8, x: 80, y: 55 },
    { id: 9, x: 20, y: 75 },
    { id: 10, x: 70, y: 80 },
    { id: 11, x: 90, y: 30 }
  ]

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <svg className="w-6 h-6 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          <h2 className="text-xl font-semibold text-gray-900">Live Drone Tracking on Map</h2>
        </div>

        <div className="flex items-center space-x-4">
          {/* Dropdown Filter */}
          <select className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option>Active Drones - 11</option>
            <option>All Drones</option>
            <option>Inactive Drones</option>
          </select>

          {/* Full Screen Button */}
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
            </svg>
            View full screen
          </button>
        </div>
      </div>

      {/* Map Container */}
      <div className="relative bg-gray-100 rounded-lg overflow-hidden" style={{ height: '500px' }}>
        {/* Map Background - Simulated city map */}
        <div className="absolute inset-0 bg-gradient-to-br from-green-100 via-blue-50 to-green-50">
          {/* Rivers/Water bodies */}
          <div className="absolute top-20 left-10 w-96 h-8 bg-blue-200 rounded-full transform rotate-12 opacity-60"></div>
          <div className="absolute bottom-32 right-20 w-80 h-12 bg-blue-200 rounded-full transform -rotate-6 opacity-60"></div>
          
          {/* Parks/Green areas */}
          <div className="absolute top-16 right-24 w-32 h-32 bg-green-200 rounded-lg opacity-50"></div>
          <div className="absolute bottom-20 left-32 w-40 h-28 bg-green-200 rounded-lg opacity-50"></div>
          <div className="absolute top-40 left-1/2 w-24 h-24 bg-green-200 rounded-full opacity-50"></div>
          
          {/* Roads/Streets - Grid pattern */}
          <div className="absolute inset-0">
            {/* Horizontal roads */}
            {[...Array(8)].map((_, i) => (
              <div
                key={`h-${i}`}
                className="absolute w-full h-1 bg-gray-300 opacity-40"
                style={{ top: `${15 + i * 12}%` }}
              ></div>
            ))}
            {/* Vertical roads */}
            {[...Array(10)].map((_, i) => (
              <div
                key={`v-${i}`}
                className="absolute h-full w-1 bg-gray-300 opacity-40"
                style={{ left: `${10 + i * 10}%` }}
              ></div>
            ))}
          </div>

          {/* Building blocks */}
          <div className="absolute top-24 left-20 w-16 h-12 bg-gray-200 opacity-30 rounded"></div>
          <div className="absolute top-48 right-32 w-20 h-16 bg-gray-200 opacity-30 rounded"></div>
          <div className="absolute bottom-40 left-1/3 w-18 h-14 bg-gray-200 opacity-30 rounded"></div>
        </div>

        {/* Drone Icons */}
        {dronePositions.map((drone) => (
          <div
            key={drone.id}
            className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group"
            style={{ left: `${drone.x}%`, top: `${drone.y}%` }}
          >
            {/* Drone Icon */}
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center shadow-lg hover:bg-green-600 transition-colors duration-200 group-hover:scale-110 transform">
              <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" />
              </svg>
            </div>
            
            {/* Tooltip */}
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
              Drone #{drone.id}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default LiveMap
