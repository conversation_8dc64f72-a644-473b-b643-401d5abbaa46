const Sidebar = () => {
  const menuItems = [
    { icon: '📊', label: 'Dashboard', active: true },
    { icon: '🗺️', label: 'Map', active: false },
    { icon: '🏢', label: 'Organizations', active: false },
    { icon: '📦', label: 'Inventory', active: false },
    { icon: '🚀', label: 'Deployment', active: false },
    { icon: '🚁', label: 'Drones', active: false },
    { icon: '🔔', label: 'Notifications', active: false },
  ]

  return (
    <div className="w-64 bg-white shadow-lg flex flex-col">
      {/* Logo Section */}
      <div className="p-6 border-b border-gray-200">
        <h1 className="text-2xl font-bold text-blue-600">S.H.A.K.T.I</h1>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 px-4 py-6">
        <ul className="space-y-2">
          {menuItems.map((item, index) => (
            <li key={index}>
              <a
                href="#"
                className={`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  item.active
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <span className="mr-3 text-lg">{item.icon}</span>
                {item.label}
              </a>
            </li>
          ))}
        </ul>
      </nav>

      {/* Bottom Section */}
      <div className="p-4 border-t border-gray-200">
        {/* Company Logo */}
        <div className="mb-4 p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
          <div className="text-white text-center">
            <div className="text-lg font-bold">RPM</div>
            <div className="text-xs">AEROSPACE</div>
          </div>
        </div>

        {/* Logout Button */}
        <button className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200">
          <span className="mr-2">Logout</span>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
          </svg>
        </button>
      </div>
    </div>
  )
}

export default Sidebar
